import pygame
import random
import sys
from enum import Enum

# 初始化pygame
pygame.init()

# 游戏常量
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
GRID_SIZE = 20
GRID_WIDTH = WINDOW_WIDTH // GRID_SIZE
GRID_HEIGHT = WINDOW_HEIGHT // GRID_SIZE

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (128, 0, 128)
ORANGE = (255, 165, 0)
PINK = (255, 192, 203)

class Direction(Enum):
    UP = (0, -1)
    DOWN = (0, 1)
    LEFT = (-1, 0)
    RIGHT = (1, 0)

class FoodType(Enum):
    NORMAL = 1
    SPEED_BOOST = 2
    SLOW_DOWN = 3
    GROW_EXTRA = 4
    SHRINK = 5

class SnakeGame:
    def __init__(self):
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("超级贪吃蛇游戏")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
        self.reset_game()
        
    def reset_game(self):
        # 蛇的初始位置
        self.snake = [(GRID_WIDTH // 2, GRID_HEIGHT // 2)]
        self.direction = Direction.RIGHT
        self.next_direction = Direction.RIGHT
        
        # 游戏状态
        self.score = 0
        self.level = 1
        self.speed = 8
        self.base_speed = 8
        self.speed_boost_timer = 0
        self.slow_timer = 0
        
        # 食物
        self.food = self.generate_food()
        self.food_type = FoodType.NORMAL
        self.special_food_timer = 0
        
        # 游戏状态
        self.game_over = False
        self.paused = False
        
    def generate_food(self):
        while True:
            food = (random.randint(0, GRID_WIDTH - 1), random.randint(0, GRID_HEIGHT - 1))
            if food not in self.snake:
                return food
    
    def generate_special_food(self):
        # 随机生成特殊食物类型
        food_types = list(FoodType)
        self.food_type = random.choice(food_types)
        self.special_food_timer = 300  # 5秒后消失
        
    def update_snake(self):
        if self.game_over or self.paused:
            return
            
        # 更新方向
        self.direction = self.next_direction
        
        # 移动蛇头
        head_x, head_y = self.snake[0]
        dx, dy = self.direction.value
        new_head = (head_x + dx, head_y + dy)
        
        # 检查边界碰撞
        if (new_head[0] < 0 or new_head[0] >= GRID_WIDTH or 
            new_head[1] < 0 or new_head[1] >= GRID_HEIGHT):
            self.game_over = True
            return
            
        # 检查自身碰撞
        if new_head in self.snake:
            self.game_over = True
            return
            
        self.snake.insert(0, new_head)
        
        # 检查是否吃到食物
        if new_head == self.food:
            self.handle_food_eaten()
        else:
            self.snake.pop()
            
        # 更新特效计时器
        if self.speed_boost_timer > 0:
            self.speed_boost_timer -= 1
            if self.speed_boost_timer == 0:
                self.speed = self.base_speed
                
        if self.slow_timer > 0:
            self.slow_timer -= 1
            if self.slow_timer == 0:
                self.speed = self.base_speed
                
        if self.special_food_timer > 0:
            self.special_food_timer -= 1
            if self.special_food_timer == 0:
                self.food_type = FoodType.NORMAL
    
    def handle_food_eaten(self):
        # 根据食物类型处理效果
        if self.food_type == FoodType.NORMAL:
            self.score += 10
        elif self.food_type == FoodType.SPEED_BOOST:
            self.score += 20
            self.speed = min(20, self.speed + 5)
            self.speed_boost_timer = 180  # 3秒
        elif self.food_type == FoodType.SLOW_DOWN:
            self.score += 15
            self.speed = max(3, self.speed - 3)
            self.slow_timer = 180  # 3秒
        elif self.food_type == FoodType.GROW_EXTRA:
            self.score += 30
            # 额外增长2节
            for _ in range(2):
                self.snake.append(self.snake[-1])
        elif self.food_type == FoodType.SHRINK:
            self.score += 25
            # 缩短蛇身（但至少保留1节）
            if len(self.snake) > 3:
                self.snake = self.snake[:len(self.snake)//2]
        
        # 生成新食物
        self.food = self.generate_food()
        
        # 随机生成特殊食物
        if random.randint(1, 4) == 1:  # 25%概率
            self.generate_special_food()
        else:
            self.food_type = FoodType.NORMAL
            
        # 升级检查
        if self.score > self.level * 100:
            self.level += 1
            self.base_speed = min(15, self.base_speed + 1)
            if self.speed_boost_timer == 0 and self.slow_timer == 0:
                self.speed = self.base_speed
    
    def get_food_color(self):
        color_map = {
            FoodType.NORMAL: RED,
            FoodType.SPEED_BOOST: BLUE,
            FoodType.SLOW_DOWN: PURPLE,
            FoodType.GROW_EXTRA: YELLOW,
            FoodType.SHRINK: ORANGE
        }
        return color_map.get(self.food_type, RED)
    
    def draw(self):
        self.screen.fill(BLACK)
        
        # 绘制蛇
        for i, segment in enumerate(self.snake):
            x, y = segment
            color = GREEN if i == 0 else (0, 200, 0)  # 蛇头更亮
            pygame.draw.rect(self.screen, color, 
                           (x * GRID_SIZE, y * GRID_SIZE, GRID_SIZE, GRID_SIZE))
            
        # 绘制食物
        food_x, food_y = self.food
        food_color = self.get_food_color()
        pygame.draw.rect(self.screen, food_color,
                        (food_x * GRID_SIZE, food_y * GRID_SIZE, GRID_SIZE, GRID_SIZE))
        
        # 绘制UI信息
        score_text = self.font.render(f"分数: {self.score}", True, WHITE)
        level_text = self.font.render(f"等级: {self.level}", True, WHITE)
        speed_text = self.small_font.render(f"速度: {self.speed}", True, WHITE)
        
        self.screen.blit(score_text, (10, 10))
        self.screen.blit(level_text, (10, 50))
        self.screen.blit(speed_text, (10, 90))
        
        # 显示特殊效果状态
        if self.speed_boost_timer > 0:
            boost_text = self.small_font.render("加速中!", True, BLUE)
            self.screen.blit(boost_text, (10, 120))
        elif self.slow_timer > 0:
            slow_text = self.small_font.render("减速中!", True, PURPLE)
            self.screen.blit(slow_text, (10, 120))
            
        # 显示食物类型提示
        if self.food_type != FoodType.NORMAL:
            food_names = {
                FoodType.SPEED_BOOST: "加速食物",
                FoodType.SLOW_DOWN: "减速食物", 
                FoodType.GROW_EXTRA: "增长食物",
                FoodType.SHRINK: "缩短食物"
            }
            food_text = self.small_font.render(food_names[self.food_type], True, self.get_food_color())
            self.screen.blit(food_text, (WINDOW_WIDTH - 150, 10))
            
        # 游戏结束画面
        if self.game_over:
            game_over_text = self.font.render("游戏结束!", True, RED)
            restart_text = self.small_font.render("按R重新开始，按Q退出", True, WHITE)
            
            text_rect = game_over_text.get_rect(center=(WINDOW_WIDTH//2, WINDOW_HEIGHT//2))
            restart_rect = restart_text.get_rect(center=(WINDOW_WIDTH//2, WINDOW_HEIGHT//2 + 40))
            
            self.screen.blit(game_over_text, text_rect)
            self.screen.blit(restart_text, restart_rect)
            
        # 暂停画面
        if self.paused and not self.game_over:
            pause_text = self.font.render("游戏暂停", True, YELLOW)
            text_rect = pause_text.get_rect(center=(WINDOW_WIDTH//2, WINDOW_HEIGHT//2))
            self.screen.blit(pause_text, text_rect)
        
        pygame.display.flip()
    
    def handle_input(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
                
            if event.type == pygame.KEYDOWN:
                if self.game_over:
                    if event.key == pygame.K_r:
                        self.reset_game()
                    elif event.key == pygame.K_q:
                        return False
                else:
                    if event.key == pygame.K_UP and self.direction != Direction.DOWN:
                        self.next_direction = Direction.UP
                    elif event.key == pygame.K_DOWN and self.direction != Direction.UP:
                        self.next_direction = Direction.DOWN
                    elif event.key == pygame.K_LEFT and self.direction != Direction.RIGHT:
                        self.next_direction = Direction.LEFT
                    elif event.key == pygame.K_RIGHT and self.direction != Direction.LEFT:
                        self.next_direction = Direction.RIGHT
                    elif event.key == pygame.K_SPACE:
                        self.paused = not self.paused
                        
        return True
    
    def run(self):
        running = True
        while running:
            running = self.handle_input()
            self.update_snake()
            self.draw()
            self.clock.tick(self.speed)
            
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = SnakeGame()
    game.run()
